import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch all doctors with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const search = searchParams.get("search") || "";
    const clinicId = searchParams.get("clinic_id");

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (search) {
      where.OR = [
        {
          profile: {
            display_name: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          profile: {
            email: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          specialization_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    if (clinicId) {
      where.clinic_id = parseInt(clinicId);
    }

    const [doctors, total] = await Promise.all([
      prisma.doctors.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          profile: {
            select: {
              id: true,
              display_name: true,
              email: true,
              phone: true,
              auth_id: true,
              created_at: true,
              updated_at: true,
            },
          },
          clinic: {
            select: {
              id: true,
              clinic_name: true,
              address: true,
              city: {
                select: {
                  id: true,
                  city_name: true,
                  state_name: true,
                },
              },
            },
          },
          _count: {
            select: {
              appointments: true,
              time_slots: true,
            },
          },
        },
        orderBy: { 
          profile: { 
            display_name: 'asc' 
          } 
        }
      }),
      prisma.doctors.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        doctors: serializePrismaResponse(doctors),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching doctors:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch doctors' },
      { status: 500 }
    );
  }
}
