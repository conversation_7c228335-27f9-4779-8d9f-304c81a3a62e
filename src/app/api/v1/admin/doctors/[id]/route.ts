import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const authId = (await params)?.id; // This is the UUID from user.id

    // First, find the profile using auth_id
    const profile = await prisma.profiles.findUnique({
      where: {
        auth_id: authId,
      },
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, message: "Profile not found" },
        { status: 404 }
      );
    }

    // Then, find the doctor using profile.id (BigInt)
    const doctor = await prisma.doctors.findUnique({
      where: {
        id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
      include: {
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            created_at: true,
            updated_at: true,
          },
        },
        clinic: {
          select: {
            id: true,
            clinic_name: true,
            address: true,
            city: {
              select: {
                id: true,
                city_name: true,
                state_name: true,
              },
            },
          },
        },
      },
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, message: "Doctor not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctor),
    });

  } catch (error) {
    console.error("Error fetching doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const authId = (await params)?.id; // This is the UUID from user.id

    // First, find the profile using auth_id
    const profile = await prisma.profiles.findUnique({
      where: {
        auth_id: authId,
      },
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, message: "Profile not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { specialization_name, clinic_id } = body;

    // Update doctor information using profile.id
    const updatedDoctor = await prisma.doctors.update({
      where: {
        id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
      data: {
        specialization_name,
        clinic_id: clinic_id ? parseInt(clinic_id) : null,
        updated_at: new Date(),
      },
      include: {
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            created_at: true,
            updated_at: true,
          },
        },
        clinic: {
          select: {
            id: true,
            clinic_name: true,
            address: true,
            city: {
              select: {
                id: true,
                city_name: true,
                state_name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedDoctor),
      message: "Doctor updated successfully",
    });
  } catch (error) {
    console.error("Error updating doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const authId = (await params)?.id; // This is the UUID from user.id

    // First, find the profile using auth_id
    const profile = await prisma.profiles.findUnique({
      where: {
        auth_id: authId,
      },
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, message: "Profile not found" },
        { status: 404 }
      );
    }

    // Check if doctor has any appointments
    const existingAppointments = await prisma.appointments.findFirst({
      where: {
        doctor_id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
    });

    if (existingAppointments) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Cannot delete doctor with existing appointments" 
        },
        { status: 400 }
      );
    }

    // Check if doctor has any time slots
    const existingTimeSlots = await prisma.time_slots.findFirst({
      where: {
        doctor_id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
    });

    if (existingTimeSlots) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Cannot delete doctor with existing time slots" 
        },
        { status: 400 }
      );
    }

    // Delete doctor
    await prisma.doctors.delete({
      where: {
        id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
    });

    return NextResponse.json({
      success: true,
      message: "Doctor deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
