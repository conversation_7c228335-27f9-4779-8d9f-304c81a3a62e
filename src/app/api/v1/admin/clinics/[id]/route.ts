import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch a specific clinic
export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const clinicId = parseInt((await params)?.id);

    if (isNaN(clinicId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid clinic ID' },
        { status: 400 }
      );
    }

    const clinic = await prisma.clinics.findUnique({
      where: { id: clinicId },
      include: {
        city: {
          select: {
            city_name: true,
            state_name: true
          }
        },
        _count: {
          select: {
            doctors: true,
            appointments: true
          }
        }
      }
    });

    if (!clinic) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(clinic)
    });
  } catch (error) {
    console.error('Error fetching clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch clinic' },
      { status: 500 }
    );
  }
}

// PUT - Update a clinic
export async function PUT(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const clinicId = parseInt((await params)?.id);
    const body = await request.json();
    const {
      clinicName,
      address,
      cityId,
      contactInfo,
      latitude,
      longitude
    } = body;

    if (isNaN(clinicId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid clinic ID' },
        { status: 400 }
      );
    }

    if (!clinicName) {
      return NextResponse.json(
        { success: false, error: 'Clinic name is required' },
        { status: 400 }
      );
    }

    // Check if clinic exists
    const existingClinic = await prisma.clinics.findUnique({
      where: { id: clinicId }
    });

    if (!existingClinic) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    // Validate city exists if provided
    if (cityId) {
      const city = await prisma.cities.findUnique({
        where: { id: parseInt(cityId) }
      });

      if (!city) {
        return NextResponse.json(
          { success: false, error: 'Invalid city ID' },
          { status: 400 }
        );
      }
    }

    const updatedClinic = await prisma.clinics.update({
      where: { id: clinicId },
      data: {
        clinic_name: clinicName,
        address,
        city_id: cityId ? parseInt(cityId) : null,
        contact_info: contactInfo,
        latitude: latitude ? parseFloat(latitude) : null,
        longitude: longitude ? parseFloat(longitude) : null
      },
      include: {
        city: {
          select: {
            city_name: true,
            state_name: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedClinic)
    });
  } catch (error) {
    console.error('Error updating clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update clinic' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a clinic
export async function DELETE(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const clinicId = parseInt((await params)?.id);

    if (isNaN(clinicId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid clinic ID' },
        { status: 400 }
      );
    }

    // Check if clinic has associated doctors or appointments
    const clinicWithRelations = await prisma.clinics.findUnique({
      where: { id: clinicId },
      include: {
        _count: {
          select: {
            doctors: true,
            appointments: true
          }
        }
      }
    });

    if (!clinicWithRelations) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    if (clinicWithRelations._count.doctors > 0 || clinicWithRelations._count.appointments > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete clinic with associated doctors or appointments' },
        { status: 409 }
      );
    }

    await prisma.clinics.delete({
      where: { id: clinicId }
    });

    return NextResponse.json({
      success: true,
      message: 'Clinic deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete clinic' },
      { status: 500 }
    );
  }
}
