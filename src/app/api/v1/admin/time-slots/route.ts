import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch time slots with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");
    const date = searchParams.get("date");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");
    const isAvailable = searchParams.get("is_available");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const where: any = {
      doctor_id: parseInt(doctorId)
    };

    if (date) {
      where.date = new Date(date);
    } else if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    }

    if (isAvailable !== null && isAvailable !== undefined) {
      where.is_available = isAvailable === 'true';
    }

    const timeSlots = await prisma.time_slots.findMany({
      where,
      orderBy: [
        { date: 'asc' },
        { start_time: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(timeSlots)
    });
  } catch (error) {
    console.error('Error fetching time slots:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch time slots' },
      { status: 500 }
    );
  }
}

// POST - Create time slots (bulk)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { doctorId, slots } = body;

    if (!doctorId || !slots || !Array.isArray(slots)) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID and slots array are required' },
        { status: 400 }
      );
    }

    // Validate doctor exists
    const doctor = await prisma.doctors.findUnique({
      where: { id: parseInt(doctorId) }
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, error: 'Doctor not found' },
        { status: 404 }
      );
    }

    // Create time slots
    const createdSlots = await prisma.time_slots.createMany({
      data: slots.map(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (slot: any) => ({
        doctor_id: parseInt(doctorId),
        date: new Date(slot.date),
        start_time: new Date(slot.startTime),
        end_time: new Date(slot.endTime),
        duration: slot.duration || 15,
        is_available: slot.isAvailable !== undefined ? slot.isAvailable : true,
        fee: slot.fee ? parseFloat(slot.fee) : null,
        currency: slot.currency
      })),
      skipDuplicates: true
    });

    return NextResponse.json({
      success: true,
      data: {
        created: createdSlots.count
      }
    });
  } catch (error) {
    console.error('Error creating time slots:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create time slots' },
      { status: 500 }
    );
  }
}

// PUT - Update time slots (bulk)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { updates } = body;

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { success: false, error: 'Updates array is required' },
        { status: 400 }
      );
    }

    const updatePromises = updates.map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      async (update: any) => {
      const { id, isAvailable, fee, currency } = update;

      return prisma.time_slots.update({
        where: { id: parseInt(id) },
        data: {
          is_available: isAvailable !== undefined ? isAvailable : undefined,
          fee: fee !== undefined ? parseFloat(fee) : undefined,
          currency: currency !== undefined ? currency : undefined
        }
      });
    });

    const updatedSlots = await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      data: updatedSlots
    });
  } catch (error) {
    console.error('Error updating time slots:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update time slots' },
      { status: 500 }
    );
  }
}
