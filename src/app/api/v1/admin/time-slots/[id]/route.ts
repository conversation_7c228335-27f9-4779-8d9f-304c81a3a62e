import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

// DELETE - Delete individual time slot
export async function DELETE(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const id = (await params)?.id;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Time slot ID is required' },
        { status: 400 }
      );
    }

    await prisma.time_slots.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'Time slot deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting time slot:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete time slot' },
      { status: 500 }
    );
  }
}
