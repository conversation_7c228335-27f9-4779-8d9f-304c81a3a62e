import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch doctors by clinic for public use (appointment booking)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clinicId = searchParams.get("clinic_id");
    const search = searchParams.get("search") || "";

    if (!clinicId) {
      return NextResponse.json(
        { success: false, error: 'Clinic ID is required' },
        { status: 400 }
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      clinic_id: parseInt(clinicId)
    };

    if (search) {
      where.OR = [
        {
          profile: {
            display_name: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          specialization_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    const doctors = await prisma.doctors.findMany({
      where,
      select: {
        id: true,
        specialization_name: true,
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true
          }
        },
        clinic: {
          select: {
            id: true,
            clinic_name: true,
            address: true,
            city: {
              select: {
                id: true,
                city_name: true,
                state_name: true
              }
            }
          }
        },
        _count: {
          select: {
            time_slots: {
              where: {
                is_available: true,
                date: {
                  gte: new Date()
                }
              }
            }
          }
        }
      },
      orderBy: { 
        profile: { 
          display_name: 'asc' 
        } 
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctors)
    });
  } catch (error) {
    console.error('Error fetching doctors:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch doctors' },
      { status: 500 }
    );
  }
}
