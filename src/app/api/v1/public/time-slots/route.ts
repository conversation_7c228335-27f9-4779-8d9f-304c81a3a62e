import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch available time slots for a doctor on specific dates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

    // Default to next 7 days if no date range provided
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    // Ensure we're looking at future dates only
    const now = new Date();
    if (start < now) {
      start.setTime(now.getTime());
    }

    const timeSlots = await prisma.time_slots.findMany({
      where: {
        doctor_id: parseInt(doctorId),
        is_available: true,
        date: {
          gte: start,
          lte: end
        },
        start_time: {
          gte: now // Only future time slots
        }
      },
      select: {
        id: true,
        date: true,
        start_time: true,
        end_time: true,
        duration: true,
        fee: true,
        currency: true,
        doctor: {
          select: {
            profile: {
              select: {
                display_name: true
              }
            }
          }
        }
      },
      orderBy: [
        { date: 'asc' },
        { start_time: 'asc' }
      ]
    });

    // Group time slots by date for easier frontend consumption
    const groupedSlots = timeSlots.reduce((acc: any, slot: any) => {
      const dateKey = slot.date.toISOString().split('T')[0];
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push({
        id: slot.id,
        start_time: slot.start_time,
        end_time: slot.end_time,
        duration: slot.duration,
        fee: slot.fee,
        currency: slot.currency,
        doctor_name: slot.doctor.profile.display_name
      });
      return acc;
    }, {});

    return NextResponse.json({
      success: true,
      data: {
        slots: serializePrismaResponse(timeSlots),
        grouped_by_date: serializePrismaResponse(groupedSlots)
      }
    });
  } catch (error) {
    console.error('Error fetching time slots:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch time slots' },
      { status: 500 }
    );
  }
}
