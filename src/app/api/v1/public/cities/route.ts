import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch all cities for public use (appointment booking)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (search) {
      where.OR = [
        {
          city_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          state_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    // Only fetch cities that have clinics with doctors
    const cities = await prisma.cities.findMany({
      where: {
        ...where,
        clinics: {
          some: {
            doctors: {
              some: {}
            }
          }
        }
      },
      select: {
        id: true,
        city_name: true,
        state_name: true,
        lat:true,
        lng:true,
        _count: {
          select: {
            clinics: true
          }
        }
      },
      orderBy: { city_name: 'asc' }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(cities)
    });
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch cities' },
      { status: 500 }
    );
  }
}
