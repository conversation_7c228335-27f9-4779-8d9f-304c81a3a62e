"use client";
import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ShadcnUI/dropdown-menu";
import { Badge } from "@/components/ShadcnUI/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  UserPlus,
  Stethoscope,
  MapPin,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";
import Avatar from "@/components/shared/Avatar/Avatar";

interface Doctor {
  id: number;
  profile: {
    id: number;
    display_name: string;
    email: string;
    phone?: string;
    auth_id: string;
  };
  specialization_name?: string;
  clinic?: {
    id: number;
    clinic_name: string;
    address?: string;
    city?: {
      id: number;
      city_name: string;
      state_name?: string;
    };
  };
  _count?: {
    appointments: number;
    time_slots: number;
  };
}

export default function DoctorsPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [clinicFilter, setClinicFilter] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDoctors, setTotalDoctors] = useState(0);

  useEffect(() => {
    setTitle("Doctors");
    setSubtitle("Manage doctor profiles and availability");
  }, [setTitle, setSubtitle]);

  const fetchDoctors = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(clinicFilter && { clinic_id: clinicFilter }),
      });

      const response = await fetch(`/api/v1/admin/doctors?${params}`);
      const data = await response.json();

      if (data.success) {
        setDoctors(data.data);
        setTotalPages(data.pagination.totalPages);
        setTotalDoctors(data.pagination.total);
      }
    } catch (error) {
      console.error("Error fetching doctors:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDoctors();
  }, [page, searchTerm, clinicFilter]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1);
  };

  const handleClinicFilter = (value: string) => {
    setClinicFilter(value === "all" ? "" : value);
    setPage(1);
  };

  const getAvailabilityStatus = (doctor: Doctor) => {
    const hasTimeSlots = doctor._count?.time_slots && doctor._count.time_slots > 0;
    return hasTimeSlots;
  };

  const formatLocation = (doctor: Doctor) => {
    if (!doctor.clinic) return "No clinic assigned";
    
    const parts = [doctor.clinic.clinic_name];
    if (doctor.clinic.city) {
      parts.push(doctor.clinic.city.city_name);
      if (doctor.clinic.city.state_name) {
        parts.push(doctor.clinic.city.state_name);
      }
    }
    return parts.join(", ");
  };

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
            <Stethoscope className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDoctors}</div>
            <p className="text-xs text-muted-foreground">
              Registered doctors
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {doctors.filter(d => getAvailabilityStatus(d)).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With time slots
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unavailable</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {doctors.filter(d => !getAvailabilityStatus(d)).length}
            </div>
            <p className="text-xs text-muted-foreground">
              No time slots
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Appointments</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {doctors.reduce((sum, d) => sum + (d._count?.appointments || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total appointments
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search doctors by name, email, or specialization..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={clinicFilter || "all"} onValueChange={handleClinicFilter}>
          <SelectTrigger className="w-[200px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by clinic" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Clinics</SelectItem>
            {/* Add clinic options dynamically */}
          </SelectContent>
        </Select>

        <Button>
          <UserPlus className="h-4 w-4 mr-2" />
          Add Doctor
        </Button>
      </div>

      {/* Doctors Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Doctor</TableHead>
              <TableHead>Specialization</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Availability</TableHead>
              <TableHead>Appointments</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  Loading doctors...
                </TableCell>
              </TableRow>
            ) : doctors.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  No doctors found
                </TableCell>
              </TableRow>
            ) : (
              doctors.map((doctor) => (
                <TableRow key={doctor.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar
                        src="/assets/avatar.jpg"
                        alt={doctor.profile.display_name}
                        width={40}
                        height={40}
                      />
                      <div>
                        <div className="font-medium">
                          {doctor.profile.display_name || "No name"}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {doctor.profile.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {doctor.specialization_name || "Not specified"}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="truncate max-w-[200px]">
                        {formatLocation(doctor)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={getAvailabilityStatus(doctor) ? "default" : "secondary"}
                      className={getAvailabilityStatus(doctor) ? "bg-green-100 text-green-800" : ""}
                    >
                      {getAvailabilityStatus(doctor) ? "Available" : "Unavailable"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {doctor._count?.appointments || 0} appointments
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Doctor
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Doctor
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((page - 1) * 10) + 1} to {Math.min(page * 10, totalDoctors)} of {totalDoctors} doctors
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
    );
}
