"use client";
import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { Button } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ShadcnUI/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import { Label } from "@/components/ShadcnUI/label";
import { Badge } from "@/components/ShadcnUI/badge";
import { Search, Plus, Edit, Trash2, MapPin } from "lucide-react";

interface City {
  id: number;
  city_name: string;
  state_name?: string;
}

interface Clinic {
  id: number;
  clinic_name: string;
  address?: string;
  city_id?: number;
  contact_info?: string;
  latitude?: number;
  longitude?: number;
  city?: {
    city_name: string;
    state_name?: string;
  };
  _count: {
    doctors: number;
    appointments: number;
  };
}

export default function ClinicsPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState("");
  const [cityFilter, setCityFilter] = useState("all");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingClinic, setEditingClinic] = useState<Clinic | null>(null);
  const [formData, setFormData] = useState({
    clinicName: "",
    address: "",
    cityId: "",
    contactInfo: "",
    latitude: "",
    longitude: "",
  });

  useEffect(() => {
    setTitle("Clinics");
    setSubtitle("Manage clinics and their locations");
  }, [setTitle, setSubtitle]);

  useEffect(() => {
    fetchClinics();
    fetchCities();
  }, [page, search, cityFilter]);

  const fetchClinics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: "10",
        search,
      });

      // Only add city filter if it's not "all"
      if (cityFilter && cityFilter !== "all") {
        params.append("city_id", cityFilter);
      }

      const response = await fetch(`/api/v1/admin/clinics?${params}`);
      const data = await response.json();

      if (data.success) {
        setClinics(data.data.clinics);
        setTotalPages(data.data.totalPages);
      }
    } catch (error) {
      console.error("Error fetching clinics:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCities = async () => {
    try {
      const response = await fetch("/api/v1/admin/cities");
      const data = await response.json();

      if (data.success) {
        setCities(data.data.cities);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingClinic 
        ? `/api/v1/admin/clinics/${editingClinic.id}`
        : "/api/v1/admin/clinics";
      
      const method = editingClinic ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clinicName: formData.clinicName,
          address: formData.address,
          cityId: formData.cityId || null,
          contactInfo: formData.contactInfo,
          latitude: formData.latitude ? parseFloat(formData.latitude) : null,
          longitude: formData.longitude ? parseFloat(formData.longitude) : null,
        }),
      });

      if (response.ok) {
        setIsDialogOpen(false);
        setEditingClinic(null);
        setFormData({
          clinicName: "",
          address: "",
          cityId: "",
          contactInfo: "",
          latitude: "",
          longitude: "",
        });
        fetchClinics();
      }
    } catch (error) {
      console.error("Error saving clinic:", error);
    }
  };

  const handleEdit = (clinic: Clinic) => {
    setEditingClinic(clinic);
    setFormData({
      clinicName: clinic.clinic_name,
      address: clinic.address || "",
      cityId: clinic.city_id?.toString() || "",
      contactInfo: clinic.contact_info || "",
      latitude: clinic.latitude?.toString() || "",
      longitude: clinic.longitude?.toString() || "",
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (clinicId: number) => {
    if (!confirm("Are you sure you want to delete this clinic?")) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/admin/clinics/${clinicId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchClinics();
      }
    } catch (error) {
      console.error("Error deleting clinic:", error);
    }
  };

  const openCreateDialog = () => {
    setEditingClinic(null);
    setFormData({
      clinicName: "",
      address: "",
      cityId: "",
      contactInfo: "",
      latitude: "",
      longitude: "",
    });
    setIsDialogOpen(true);
  };

  const getLocationDisplay = (clinic: Clinic) => {
    if (clinic.latitude && clinic.longitude) {
      return `${clinic.latitude.toFixed(6)}, ${clinic.longitude.toFixed(6)}`;
    }
    return "Not set";
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search clinics..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Select
            value={cityFilter}
            onValueChange={setCityFilter}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by city" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Cities</SelectItem>
              {cities.map((city) => (
                <SelectItem key={city.id} value={city.id.toString()}>
                  {city.city_name}
                  {city.state_name && `, ${city.state_name}`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Clinic
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingClinic ? "Edit Clinic" : "Add New Clinic"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="clinicName">Clinic Name *</Label>
                  <Input
                    id="clinicName"
                    value={formData.clinicName}
                    onChange={(e) =>
                      setFormData({ ...formData, clinicName: e.target.value })
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="cityId">City</Label>
                  <Select
                    value={formData.cityId}
                    onValueChange={(value) =>
                      setFormData({ ...formData, cityId: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select city" />
                    </SelectTrigger>
                    <SelectContent>
                      {cities.map((city) => (
                        <SelectItem key={city.id} value={city.id.toString()}>
                          {city.city_name}
                          {city.state_name && `, ${city.state_name}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) =>
                    setFormData({ ...formData, address: e.target.value })
                  }
                  placeholder="Full address"
                />
              </div>
              <div>
                <Label htmlFor="contactInfo">Contact Information</Label>
                <Input
                  id="contactInfo"
                  value={formData.contactInfo}
                  onChange={(e) =>
                    setFormData({ ...formData, contactInfo: e.target.value })
                  }
                  placeholder="Phone, email, or other contact details"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="latitude">Latitude</Label>
                  <Input
                    id="latitude"
                    type="number"
                    step="any"
                    value={formData.latitude}
                    onChange={(e) =>
                      setFormData({ ...formData, latitude: e.target.value })
                    }
                    placeholder="e.g., 40.7128"
                  />
                </div>
                <div>
                  <Label htmlFor="longitude">Longitude</Label>
                  <Input
                    id="longitude"
                    type="number"
                    step="any"
                    value={formData.longitude}
                    onChange={(e) =>
                      setFormData({ ...formData, longitude: e.target.value })
                    }
                    placeholder="e.g., -74.0060"
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {editingClinic ? "Update" : "Create"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Clinics Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Clinic Name</TableHead>
              <TableHead>City</TableHead>
              <TableHead>Address</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Stats</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clinics.map((clinic) => (
              <TableRow key={clinic.id}>
                <TableCell className="font-medium">
                  {clinic.clinic_name}
                </TableCell>
                <TableCell>
                  {clinic.city ? (
                    <div>
                      <div className="font-medium">{clinic.city.city_name}</div>
                      {clinic.city.state_name && (
                        <div className="text-sm text-gray-500">
                          {clinic.city.state_name}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">Not specified</span>
                  )}
                </TableCell>
                <TableCell>
                  {clinic.address || (
                    <span className="text-gray-400">Not specified</span>
                  )}
                </TableCell>
                <TableCell>
                  {clinic.contact_info || (
                    <span className="text-gray-400">Not specified</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">
                      {getLocationDisplay(clinic)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Badge variant="secondary">
                      {clinic._count.doctors} doctor{clinic._count.doctors !== 1 ? 's' : ''}
                    </Badge>
                    <Badge variant="outline">
                      {clinic._count.appointments} appointment{clinic._count.appointments !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(clinic)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(clinic.id)}
                      disabled={clinic._count.doctors > 0 || clinic._count.appointments > 0}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Page {page} of {totalPages}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            onClick={() => setPage(page + 1)}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
