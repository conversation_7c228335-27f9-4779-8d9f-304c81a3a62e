import { useQuery } from "@tanstack/react-query";

export interface Clinic {
  centerId: number;
  name: string;
  address?: string;
  contact_info?: string;
}

export const useClinics = (cityId: number): Clinic[] => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['clinics', cityId],
    queryFn: async () => {
      const response = await fetch(`/api/v1/public/clinics?city_id=${cityId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch clinics');
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch clinics');
      }
      return result.data;
    },
    enabled: !!cityId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  if (isLoading || error || !data) {
    return [];
  }

  return data.map((clinic: any) => ({
    centerId: clinic.id,
    name: clinic.clinic_name,
    address: clinic.address,
    contact_info: clinic.contact_info,
  }));
};

export default useClinics;
