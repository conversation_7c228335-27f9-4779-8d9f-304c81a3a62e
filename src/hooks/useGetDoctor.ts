import { useQuery } from "@tanstack/react-query";

export interface DoctorDetails {
  doctorId: string;
  centerId: number;
  name: string;
  imageUrl?: string;
  specialization: string;
  experience: string;
  clinicAddress: string;
  consultationMode: string;
  bio?: string;
}

export const useGetDoctor = (doctorId: string): DoctorDetails | null => {
  const { data } = useQuery({
    queryKey: ['all-doctors'],
    queryFn: async () => {
      // First get all cities
      const citiesResponse = await fetch('/api/v1/public/cities');
      if (!citiesResponse.ok) {
        throw new Error('Failed to fetch cities');
      }
      const citiesResult = await citiesResponse.json();
      if (!citiesResult.success) {
        throw new Error(citiesResult.error || 'Failed to fetch cities');
      }

      // Then get all clinics and their doctors
      const allDoctors = [];
      for (const city of citiesResult.data) {
        const clinicsResponse = await fetch(`/api/v1/public/clinics?city_id=${city.id}`);
        if (clinicsResponse.ok) {
          const clinicsResult = await clinicsResponse.json();
          if (clinicsResult.success) {
            for (const clinic of clinicsResult.data) {
              const doctorsResponse = await fetch(`/api/v1/public/doctors?clinic_id=${clinic.id}`);
              if (doctorsResponse.ok) {
                const doctorsResult = await doctorsResponse.json();
                if (doctorsResult.success) {
                  allDoctors.push(...doctorsResult.data.map((doctor: any) => ({
                    ...doctor,
                    centerId: clinic.id
                  })));
                }
              }
            }
          }
        }
      }
      return allDoctors;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  if (!data) {
    return null;
  }

  const doctor = data.find((doctor: any) => doctor.id.toString() === doctorId);

  if (!doctor) {
    return null;
  }

  return {
    doctorId: doctor.id.toString(),
    centerId: doctor.centerId,
    name: doctor.profile.display_name || 'Dr. Unknown',
    imageUrl: undefined,
    specialization: doctor.specialization_name || 'General Practitioner',
    experience: 'Available',
    clinicAddress: doctor.clinic ?
      `${doctor.clinic.clinic_name}, ${doctor.clinic.city?.city_name || ''}` :
      'Clinic not specified',
    consultationMode: 'In-Person Consultation',
    bio: undefined,
  };
};

export default useGetDoctor;
