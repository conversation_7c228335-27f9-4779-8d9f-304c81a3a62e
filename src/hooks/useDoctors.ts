import { useQuery } from "@tanstack/react-query";

export interface Doctor {
  doctorId: string;
  name: string;
  imageUrl?: string;
  specialization: string;
  experience: string;
  clinicAddress: string;
  consultationMode: string;
  bio?: string;
  availableSlots?: number;
}

export const useDoctors = (centerId: number): Doctor[] => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['doctors', centerId],
    queryFn: async () => {
      const response = await fetch(`/api/v1/public/doctors?clinic_id=${centerId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch doctors');
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch doctors');
      }
      return result.data;
    },
    enabled: !!centerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  if (isLoading || error || !data) {
    return [];
  }

  return data.map((doctor: any) => ({
    doctorId: doctor.id.toString(),
    name: doctor.profile.display_name || 'Dr. Unknown',
    imageUrl: undefined, // We'll use default avatar for now
    specialization: doctor.specialization_name || 'General Practitioner',
    experience: 'Available', // We don't have experience data in the new schema
    clinicAddress: doctor.clinic ?
      `${doctor.clinic.clinic_name}, ${doctor.clinic.city?.city_name || ''}` :
      'Clinic not specified',
    consultationMode: 'In-Person Consultation', // Default for now
    bio: undefined,
    availableSlots: doctor._count?.time_slots || 0,
  }));
};

export default useDoctors;
