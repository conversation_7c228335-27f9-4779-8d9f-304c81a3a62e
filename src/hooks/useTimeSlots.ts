import { useQuery } from "@tanstack/react-query";

export interface TimeSlot {
  id: number;
  start_time: string;
  end_time: string;
  duration: number;
  fee?: number;
  currency?: string;
  doctor_name?: string;
}

export interface GroupedTimeSlots {
  [date: string]: TimeSlot[];
}

export interface UseTimeSlotsResult {
  slots: TimeSlot[];
  groupedByDate: GroupedTimeSlots;
  loading: boolean;
  error: string | null;
}

export const useTimeSlots = (
  doctorId: number | string,
  startDate?: string,
  endDate?: string
): UseTimeSlotsResult => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['timeSlots', doctorId, startDate, endDate],
    queryFn: async () => {
      const params = new URLSearchParams({
        doctor_id: doctorId.toString(),
      });
      
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);

      const response = await fetch(`/api/v1/public/time-slots?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch time slots');
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch time slots');
      }
      return result.data;
    },
    enabled: !!doctorId,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for time-sensitive data)
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    slots: data?.slots || [],
    groupedByDate: data?.grouped_by_date || {},
    loading: isLoading,
    error: error ? (error as Error).message : null,
  };
};

export default useTimeSlots;
