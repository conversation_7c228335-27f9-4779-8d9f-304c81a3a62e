import React, { useState } from "react";
import Button, { ButtonType } from "../shared/Button/Button";
import Input from "../shared/Input/Input";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/contexts/ToastContext";

interface OTPEmailStepProps {
  onBack: () => void;
  onEmailSent: (email: string) => void;
}

const OTPEmailStep: React.FC<OTPEmailStepProps> = ({
  onBack,
  onEmailSent,
}) => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const toast = useToast();
  const supabase = createClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (email.trim()) {
      setIsLoading(true);
      setErrorMessage(null);

      try {
        const { error } = await supabase.auth.signInWithOtp({
          email,
          options: {
            shouldCreateUser: false,
          },
        });

        if (error) {
          setErrorMessage(error.message);
        } else {
          toast.success("Verification code sent to your email!");
          onEmailSent(email);
        }
      } catch (error) {
        console.error("OTP sending error:", error);
        setErrorMessage("Failed to send verification code. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
      <main className="flex-1 flex items-center justify-center px-6 py-16">
        <div className="w-full flex justify-center">
          <div className="w-[20.063rem] md:w-[26.5rem] flex flex-col justify-center gap-12">
            {/* Back Button */}
            <button
              onClick={onBack}
              className="flex items-center gap-2 text-[var(--grey-6)] hover:text-[var(--grey-7)] transition-colors duration-200 self-start cursor-pointer"
            >
              <ArrowLeft size={20} />
              Back to Login
            </button>

            {/* Title */}
            <div className="text-center flex flex-col">
              <h1 className="text-center pt-[0.563rem] text-2xl font-semibold text-[var(--grey-7)] mb-2">
                Login with OTP
                <div className="flex justify-center py-1">
                  <Image
                    src="/assets/loginPage/Line.png"
                    alt="Decorative line"
                    className="h-1 w-16"
                    width={100}
                    height={9}
                  />
                </div>
              </h1>

              <p className="text-[var(--grey-6)] text-base font-medium">
                Enter your email address to receive a
                <br />
                verification code for secure login.
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit}>
              <div className="space-y-6">
                {/* Email Field */}
                <div className="flex flex-col gap-2">
                  <Input
                    type="email"
                    label="Email Address"
                    value={email}
                    onChange={setEmail}
                    placeholder="Enter your email address"
                    maxLength={50}
                    required
                  />
                </div>

                {/* Error Message */}
                {errorMessage && (
                  <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
                    {errorMessage}
                  </div>
                )}

                {/* Login with OTP Button */}
                <div className="pt-2">
                  <Button
                    type={ButtonType.PRIMARY}
                    text={isLoading ? "Sending..." : "Login with OTP"}
                    onClick={() => {
                      if (email.trim()) {
                        handleSubmit({ preventDefault: () => { } } as React.FormEvent);
                      }
                    }}
                    disabled={isLoading || !email.trim()}
                  />
                </div>
              </div>
            </form>
          </div>
        </div>
      </main>
  );
};

export default OTPEmailStep;
