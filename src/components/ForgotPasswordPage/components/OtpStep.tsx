/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from "react";
import Image from "next/image";
import Button, { ButtonType } from "../../shared/Button/Button";
import OTPInput from "@/components/shared/OTPInput/OTPInput";
import { useCountdown } from "@/hooks/useCountdown";
import { createClient } from "@/utils/supabase/client";
import PageHeader from "@/components/shared/PageHeader";

interface OtpStepProps {
  localEmail: string;
  otp: string;
  setOtp: (otp: string) => void;
  handleOTPSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  errorMessage: string;
  successMessage: string;
  remainingAttempts: number | undefined;
  handleSendOTP: () => void;
  handleVerifyOTP: () => void;
}

const OtpStep: React.FC<OtpStepProps> = ({
  localEmail,
  otp,
  setOtp,
  handleOTPSubmit,
  isLoading,
  errorMessage,
  successMessage,
  remainingAttempts,
  handleSendOTP,
  handleVerifyOTP,
}) => {
  const countdown = useCountdown(120);
  const [isResending, setIsResending] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [error, setError] = useState("");
  const [userEmail, setUserEmail] = useState("");

  const handleResendCode = async () => {
    if (!userEmail) {
      setError("Email not found. Please try signing up again.");
      return;
    }

    setIsResending(true);
    setError("");

    try {
      const supabase = createClient();

      const { error: resendError } = await supabase.auth.signInWithOtp({
        email: userEmail,
        options: {
          shouldCreateUser: false, // User already exists
        },
      });

      if (resendError) {
        setError("Failed to resend verification code. Please try again.");
        console.error("Resend OTP error:", resendError);
      } else {
        console.log("Verification code resent to:", userEmail);
        // Reset the countdown timer when code is resent successfully
        countdown.reset(120); // Reset to 2 minutes
        // Optionally show a success message
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error("Resend error:", err);
    } finally {
      setIsResending(false);
    }
  };
  return (
    <>
      <div className="text-center flex flex-col">
        <PageHeader title="Enter Verification Code" />
        <p className="text-[var(--grey-6)] text-base font-medium">
          Enter the code sent to your registered email or mobile number.
          <br />
          <span className="font-semibold text-[var(--grey-7)]">
            {localEmail}
          </span>
        </p>
      </div>

      <form onSubmit={handleOTPSubmit}>
        <div className="space-y-6">
          <div className="flex flex-col gap-4">
            <label className="block text-base font-medium text-[var(--grey-6)] text-center">
              Enter Verification Code
            </label>
            <OTPInput
              length={6}
              onChange={setOtp}
              onComplete={(code) => setOtp(code)}
              disabled={isLoading}
              error={!!errorMessage}
              autoFocus
            />
          </div>

          {successMessage && (
            <div className="text-[var(--green-6)] text-sm text-center bg-green-50 border border-green-200 rounded-sm p-3">
              {successMessage}
            </div>
          )}

          {errorMessage && (
            <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
              {errorMessage}
              {remainingAttempts !== undefined && remainingAttempts > 0 && (
                <div className="mt-1">
                  {remainingAttempts} attempt
                  {remainingAttempts !== 1 ? "s" : ""} remaining
                </div>
              )}
            </div>
          )}

          {/* Resend Code Link with Countdown Timer */}
          <div className="flex items-center justify-center gap-1">
            <button
              type="button"
              onClick={handleResendCode}
              disabled={isResending || !countdown.isExpired}
              className="cursor-pointer text-[var(--grey-5)] text-base hover:text-[var(--grey-6)] transition-colors duration-200 underline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? "Resending..." : "Resend Code"}
            </button>

            {/* Countdown Timer Display */}
            {!countdown.isExpired && (
              <p className="text-[var(--grey-5)] text-base">
                in{" "}
                <span className="text-[var(--violet-11)] text-base font-bold">
                  ({countdown.formattedTime})
                </span>
              </p>
            )}
          </div>

          {/* Additional info when timer is active */}
          {!countdown.isExpired && (
            <p className="text-[var(--red-6)] text-base text-center">
              Please do not refresh or close this page.
            </p>
          )}

          <div className="pt-2">
            <Button
              type={ButtonType.PRIMARY}
              text={isLoading ? "Verifying..." : "Continue To Verify Email"}
              onClick={handleVerifyOTP}
              disabled={isLoading || otp.length !== 6}
            />
          </div>
        </div>
      </form>
    </>
  );
};

export default OtpStep;
