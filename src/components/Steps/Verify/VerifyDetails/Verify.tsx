import React from "react";
import Header, { HeaderState } from "../../../shared/Header/Header";
import Footer from "../../../shared/Footer/Footer";
import Button, { ButtonType } from "../../../shared/Button/Button";
import NeedHelp from "../../../shared/NeedHelp/NeedHelp";
import StepHeader from "../../../shared/StepHeader/StepHeader";

// Google icon component
const GoogleIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
    <path
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      fill="#4285F4"
    />
    <path
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      fill="#34A853"
    />
    <path
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      fill="#FBBC05"
    />
    <path
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      fill="#EA4335"
    />
  </svg>
);

// Email icon component
const EmailIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
    <polyline points="22,6 12,13 2,6" />
  </svg>
);

// Back arrow icon component
const BackIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M19 12H5" />
    <polyline points="12,19 5,12 12,5" />
  </svg>
);

export interface VerifyProps {
  onEmailContinue?: () => void;
  onGoogleContinue?: () => void;
  onSignIn?: () => void;
  onBack?: () => void;
  isSubmitting?: boolean;
}

const Verify: React.FC<VerifyProps> = ({
  onEmailContinue,
  onGoogleContinue,
  onSignIn,
  onBack,
  isSubmitting,
}) => {
  const handleGoogleContinue = () => {
    if (onGoogleContinue) {
      onGoogleContinue();
    }
  };

  const handleEmailContinue = () => {
    if (onEmailContinue) {
      onEmailContinue();
    }
  };

  const handleSignIn = () => {
    if (onSignIn) {
      onSignIn();
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-4 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader
            currentStep={4}
            totalSteps={5}
            title="Verify Details"
            className="mb-22.25 md:mb-15"
          />

          <div className="bg-white rounded-lg">
            <div className="space-y-6 max-w-md mx-auto flex flex-col gap-27.5">
              <div className="flex flex-col gap-20">
                <div className="flex flex-col gap-6">
                  {/* Google Continue Button */}
                  {/* TODO: Uncomment this when Google Authentication is implemented */}
                  {/* <Button
                    type={ButtonType.SECONDARY}
                    text="Continue with Google"
                    icon={<GoogleIcon />}
                    onClick={handleGoogleContinue}
                    className="border-[var(--grey-3)] text-[var(--grey-7)] hover:bg-[var(--grey-1)] hover:border-[var(--grey-3)]"
                    disabled={isSubmitting}
                  /> */}
                  {/* Email Continue Button */}
                  <Button
                    type={ButtonType.SECONDARY}
                    text="Continue with Email"
                    icon={<EmailIcon />}
                    onClick={handleEmailContinue}
                    className="border-[var(--grey-3)] text-[var(--grey-7)] hover:bg-[var(--grey-1)] hover:border-[var(--grey-3)]"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Sign In Link */}
                <div className="text-center">
                  <p className="text-[var(--grey-6)] text-base mb-1">
                    Already have an Account?
                  </p>
                  <button
                    onClick={handleSignIn}
                    disabled={isSubmitting}
                    className="bg-transparent !border-0 !p-0 text-[var(--red-6)] hover:text-[var(--red-7)] hover:bg-transparent underline font-medium cursor-pointer"
                  >
                    Sign in here
                  </button>
                </div>
              </div>

              {/* Back Button */}
              <div className="flex justify-center">
                <Button
                  type={ButtonType.SECONDARY}
                  text="Back"
                  icon={<BackIcon />}
                  onClick={handleBack}
                  className="border-[var(--grey-3)] text-[var(--grey-7)] hover:bg-[var(--grey-1)] hover:border-[var(--grey-3)]"
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};

export default Verify;
