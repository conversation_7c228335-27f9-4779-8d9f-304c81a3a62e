import React from "react";
import Button, { ButtonType } from "../../Button/Button";
import Image from "next/image";
import { useScreenWidth } from "@/hooks/useScreenWidth";

interface NoAppointmentsProps {
  onScheduleClick?: () => void;
  className?: string;
}

const NoAppointments: React.FC<NoAppointmentsProps> = ({
  onScheduleClick,
  className = "",
}) => {
  const screenWidth = useScreenWidth();
  const IconWidth = screenWidth < 1024 ? 102.76 : 106.9;
  const IconHeight = screenWidth < 1024 ? 102.76 : 148.51;

  return (
    <div
      className={`flex flex-col items-center justify-center gap-6 lg:gap-8 ${className}`}
    >
      {/* Icon */}
      <div className="flex flex-col items-center justify-center gap-1">
        <Image
          src="/assets/noAppointments.svg"
          alt="No Appointments"
          width={IconWidth}
          height={IconHeight}
        />
        {/* Heading */}
        <h2 className="text-[var(--grey-7)] font-bold text-2xl text-center">
          No Appointments Yet
        </h2>
      </div>

      {/* Description */}
      <div className="text-center max-w-[22.625rem] lg:max-w-[34rem] flex flex-col gap-2 justify-center items-center">
        <p className="text-[var(--grey-6)] text-base font-medium">
          Looks like you haven&apos;t booked any appointments.
        </p>
        <p className="text-[var(--grey-6)] text-base font-medium">
          Start your IVF journey by scheduling your first consultation with a
          specialist.
        </p>
      </div>

      {/* Call-to-Action Button */}
      <Button
        type={ButtonType.PRIMARY}
        text="Schedule Appointment"
        onClick={onScheduleClick}
        className="max-w-xs"
      />
    </div>
  );
};

export default NoAppointments;
