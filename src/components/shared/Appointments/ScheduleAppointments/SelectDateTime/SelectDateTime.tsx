import React, { useEffect, useState } from "react";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import ToggleButton from "@/components/shared/ToggleButton/ToggleButton";
import {
  ArrowLeftIcon,
  CaretDownIcon,
  CaretLeftIcon,
  CaretRightIcon,
} from "@phosphor-icons/react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { useTimeSlots } from "@/hooks/useTimeSlots";

export interface SelectDateTimeProps {
  doctorId: number | string;
  onDateTimeSelect: (date: string, time: string) => void;
  onBack: () => void;
  onCancel: () => void;
  onConfirm: () => void;
  selectedDate?: string;
  selectedTime?: string;
}

const SelectDateTime: React.FC<SelectDateTimeProps> = ({
  doctorId,
  onDateTimeSelect,
  onBack,
  onCancel,
  onConfirm,
  selectedDate,
  selectedTime,
}) => {
  const [selectedDateState, setSelectedDateState] = useState<
    string | undefined
  >(selectedDate);
  const [selectedTimeState, setSelectedTimeState] = useState<
    string | undefined
  >(selectedTime);

  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  // Fetch available time slots for the doctor
  const { groupedByDate, loading: slotsLoading, error: slotsError } = useTimeSlots(doctorId);

  useEffect(() => {
    setTitle(null);
    setSubtitle(null);
    setBreadcrumbs([
      { label: "My Appointments", href: "/user/appointments" },
      { label: "Schedule Appointments", isActive: true },
    ]);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  // Generate dates based on available slots
  const generateDates = () => {
    const dates = [];
    const today = new Date();
    const availableDates = Object.keys(groupedByDate);

    // If we have available slots, use those dates
    if (availableDates.length > 0) {
      availableDates.forEach(dateStr => {
        const date = new Date(dateStr);
        const isToday = dateStr === today.toISOString().split("T")[0];
        dates.push({
          date: dateStr,
          display: `${date.getDate()} ${date.toLocaleDateString("en-US", { month: "short" })}`,
          day: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday,
          hasSlots: true,
        });
      });
    } else {
      // Fallback: generate next 7 days even if no slots available
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateStr = date.toISOString().split("T")[0];
        dates.push({
          date: dateStr,
          display: `${date.getDate()} ${date.toLocaleDateString("en-US", { month: "short" })}`,
          day: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday: i === 0,
          hasSlots: false,
        });
      }
    }
    return dates.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const dates = generateDates();

  // Get time slots for selected date
  const getTimeSlotsForDate = (date: string) => {
    const slots = groupedByDate[date] || [];
    return slots.map(slot => {
      const startTime = new Date(slot.start_time);
      const timeString = startTime.toLocaleTimeString("en-US", {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
      return {
        id: slot.id,
        time: timeString,
        fee: slot.fee,
        currency: slot.currency,
      };
    });
  };

  const handleDateSelect = (date: string) => {
    setSelectedDateState(date);
    if (selectedTimeState) {
      onDateTimeSelect(date, selectedTimeState);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTimeState(time);
    if (selectedDateState) {
      onDateTimeSelect(selectedDateState, time);
    }
  };

  const handleTodayClick = () => {
    const today = new Date().toISOString().split("T")[0];
    handleDateSelect(today);
  };

  const isDateSelected = (date: string) => selectedDateState === date;
  const isTimeSelected = (time: string) => selectedTimeState === time;

  return (
    <div className="w-full flex flex-col items-center gap-8 md:gap-12">
      <div className="flex flex-col items-center gap-2">
        <StepHeader
          currentStep={4}
          totalSteps={4}
          title="Choose Date & Time"
          className="!mb-0"
        />

        <p className="text-center text-[var(--grey-6)] text-base font-medium">
          Choose your preferred date & time
        </p>
      </div>

      <div className="flex flex-col gap-8 xl:gap-12 w-full max-w-2xl">
        {/* Date Selection Section */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 text-[var(--grey-7)] text-base font-medium">
                <span>18 July, Tues</span>
                <CaretDownIcon size={16} />
              </div>
              <Button
                type={ButtonType.SECONDARY}
                text="Today"
                onClick={handleTodayClick}
                size="sm"
                className="!px-4 !py-2 !h-8"
              />
            </div>
            <div className="flex items-center gap-2">
              <button className="w-8 h-8 flex items-center justify-center border border-[var(--grey-3)] rounded-sm hover:bg-[var(--grey-2)]">
                <CaretLeftIcon size={16} />
              </button>
              <button className="w-8 h-8 flex items-center justify-center border border-[var(--grey-3)] rounded-sm hover:bg-[var(--grey-2)]">
                <CaretRightIcon size={16} />
              </button>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-[var(--grey-6)] text-base font-medium">
              What time best works for you
            </label>
            <div className="grid grid-cols-7 gap-2">
              {dates.map((dateInfo) => (
                <ToggleButton
                  key={dateInfo.date}
                  isSelected={isDateSelected(dateInfo.date)}
                  onClick={() => handleDateSelect(dateInfo.date)}
                  variant="compact"
                  className={`!py-2 !px-3 text-sm ${
                    !dateInfo.hasSlots ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  disabled={!dateInfo.hasSlots}
                >
                  <div className="flex flex-col items-center gap-1">
                    <span className="text-xs">{dateInfo.day}</span>
                    <span>{dateInfo.display}</span>
                    {dateInfo.hasSlots && (
                      <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                    )}
                  </div>
                </ToggleButton>
              ))}
            </div>
          </div>
        </div>

        {/* Time Selection Section */}
        <div className="flex flex-col gap-4">
          <label className="text-[var(--grey-6)] text-base font-medium">
            Select time slot for you
          </label>
          {slotsLoading ? (
            <div className="text-center py-8 text-[var(--grey-6)]">
              Loading available time slots...
            </div>
          ) : slotsError ? (
            <div className="text-center py-8 text-red-600">
              Error loading time slots: {slotsError}
            </div>
          ) : selectedDateState ? (
            <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
              {getTimeSlotsForDate(selectedDateState).map((slot) => (
                <ToggleButton
                  key={slot.id}
                  isSelected={isTimeSelected(slot.time)}
                  onClick={() => handleTimeSelect(slot.time)}
                  variant="compact"
                  className="!py-3 !px-4"
                >
                  <div className="flex flex-col items-center">
                    <span>{slot.time}</span>
                    {slot.fee && (
                      <span className="text-xs text-[var(--grey-6)]">
                        {slot.currency || '₹'}{slot.fee}
                      </span>
                    )}
                  </div>
                </ToggleButton>
              ))}
              {getTimeSlotsForDate(selectedDateState).length === 0 && (
                <div className="col-span-full text-center py-4 text-[var(--grey-6)]">
                  No available time slots for this date
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-[var(--grey-6)]">
              Please select a date to view available time slots
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between gap-4 pt-4">
          <Button
            type={ButtonType.SECONDARY}
            text="Back"
            icon={<ArrowLeftIcon size={20} />}
            onClick={onBack}
            className="!flex !flex-row-reverse"
          />

          <Button
            type={ButtonType.SECONDARY}
            text="Cancel"
            onClick={onCancel}
          />

          <Button
            type={ButtonType.PRIMARY}
            text="Confirm Appointment"
            onClick={onConfirm}
            disabled={!selectedDateState || !selectedTimeState}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectDateTime;
