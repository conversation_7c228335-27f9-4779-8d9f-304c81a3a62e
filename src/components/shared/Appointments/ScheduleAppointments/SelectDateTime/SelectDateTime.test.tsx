import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import SelectDateTime from "./SelectDateTime";

// Mock the StepHeader component
jest.mock("@/components/shared/StepHeader/StepHeader", () => {
  return function MockStepHeader({ title }: { title: string }) {
    return <div data-testid="step-header">{title}</div>;
  };
});

// Mock the Button component
jest.mock("@/components/shared/Button/Button", () => {
  const MockButton = ({
    text,
    onClick,
    disabled,
  }: {
    text: string;
    onClick: () => void;
    disabled?: boolean;
  }) => {
    return (
      <button
        data-testid={`button-${text}`}
        onClick={onClick}
        disabled={disabled}
      >
        {text}
      </button>
    );
  };

  MockButton.ButtonType = {
    PRIMARY: "primary",
    SECONDARY: "secondary",
  };

  return MockButton;
});

// Mock the ToggleButton component
jest.mock("@/components/shared/ToggleButton/ToggleButton", () => {
  return function MockToggleButton({
    children,
    isSelected,
    onClick,
  }: {
    children: React.ReactNode;
    isSelected: boolean;
    onClick: () => void;
  }) {
    return (
      <button
        data-testid={`toggle-${children}`}
        data-selected={isSelected}
        onClick={onClick}
        className="toggle-button"
      >
        {children}
      </button>
    );
  };
});

describe("SelectDateTime", () => {
  const mockOnDateTimeSelect = jest.fn();
  const mockOnBack = jest.fn();
  const mockOnCancel = jest.fn();
  const mockOnConfirm = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with correct title and subtitle", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    expect(screen.getByTestId("step-header")).toHaveTextContent(
      "Choose Date & Time"
    );
    expect(
      screen.getByText("Choose your preferred date & time")
    ).toBeInTheDocument();
  });

  it("renders date selection section", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    expect(
      screen.getByText("What time best works for you")
    ).toBeInTheDocument();
    expect(screen.getByText("Today")).toBeInTheDocument();
  });

  it("renders time selection section", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    expect(screen.getByText("Select time slot for you")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-10:00 am")).toBeInTheDocument();
    expect(screen.getByTestId("toggle-03:30 pm")).toBeInTheDocument();
  });

  it("renders action buttons", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    expect(screen.getByTestId("button-Back")).toBeInTheDocument();
    expect(screen.getByTestId("button-Cancel")).toBeInTheDocument();
    expect(
      screen.getByTestId("button-Confirm Appointment")
    ).toBeInTheDocument();
  });

  it("calls onBack when back button is clicked", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    const backButton = screen.getByTestId("button-Back");
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalled();
  });

  it("calls onCancel when cancel button is clicked", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    const cancelButton = screen.getByTestId("button-Cancel");
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it("calls onConfirm when confirm button is clicked", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedDate="2024-01-15"
        selectedTime="03:30 pm"
      />
    );

    const confirmButton = screen.getByTestId("button-Confirm Appointment");
    fireEvent.click(confirmButton);

    expect(mockOnConfirm).toHaveBeenCalled();
  });

  it("disables confirm button when no date and time are selected", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    const confirmButton = screen.getByTestId("button-Confirm Appointment");
    expect(confirmButton).toBeDisabled();
  });

  it("enables confirm button when both date and time are selected", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedDate="2024-01-15"
        selectedTime="03:30 pm"
      />
    );

    const confirmButton = screen.getByTestId("button-Confirm Appointment");
    expect(confirmButton).not.toBeDisabled();
  });

  it("calls onDateTimeSelect when a date is clicked", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedTime="03:30 pm"
      />
    );

    // Get today's date
    const today = new Date().toISOString().split("T")[0];
    const todayDisplay = new Date().toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
    });

    const dateButton = screen.getByTestId(`toggle-${todayDisplay}`);
    fireEvent.click(dateButton);

    expect(mockOnDateTimeSelect).toHaveBeenCalledWith(today, "03:30 pm");
  });

  it("calls onDateTimeSelect when a time is clicked", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedDate="2024-01-15"
      />
    );

    const timeButton = screen.getByTestId("toggle-03:30 pm");
    fireEvent.click(timeButton);

    expect(mockOnDateTimeSelect).toHaveBeenCalledWith("2024-01-15", "03:30 pm");
  });

  it("calls onDateTimeSelect when Today button is clicked", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedTime="03:30 pm"
      />
    );

    const todayButton = screen.getByTestId("button-Today");
    fireEvent.click(todayButton);

    const today = new Date().toISOString().split("T")[0];
    expect(mockOnDateTimeSelect).toHaveBeenCalledWith(today, "03:30 pm");
  });

  it("shows selected date when selectedDate is provided", () => {
    const today = new Date().toISOString().split("T")[0];
    const todayDisplay = new Date().toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
    });

    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedDate={today}
      />
    );

    const dateButton = screen.getByTestId(`toggle-${todayDisplay}`);
    expect(dateButton).toHaveAttribute("data-selected", "true");
  });

  it("shows selected time when selectedTime is provided", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
        selectedTime="03:30 pm"
      />
    );

    const timeButton = screen.getByTestId("toggle-03:30 pm");
    expect(timeButton).toHaveAttribute("data-selected", "true");
  });

  it("renders all time slots", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    const expectedTimeSlots = [
      "10:00 am",
      "10:30 am",
      "11:30 am",
      "12:00 pm",
      "12:30 pm",
      "03:00 pm",
      "03:30 pm",
      "04:30 pm",
      "05:00 pm",
      "05:30 pm",
      "06:30 pm",
      "07:00 am",
      "07:30 pm",
      "08:30 am",
    ];

    expectedTimeSlots.forEach((time) => {
      expect(screen.getByTestId(`toggle-${time}`)).toBeInTheDocument();
    });
  });

  it("renders 7 days of dates", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    const toggleButtons = screen.getAllByTestId(/toggle-\d+ \w+/);
    expect(toggleButtons).toHaveLength(7);
  });

  it("handles navigation arrows", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    // Navigation arrows should be present but functionality would be implemented later
    const leftArrow = screen.getByRole("button", { name: "" });
    const rightArrow = screen.getByRole("button", { name: "" });

    expect(leftArrow).toBeInTheDocument();
    expect(rightArrow).toBeInTheDocument();
  });

  it("renders with correct accessibility attributes", () => {
    render(
      <SelectDateTime
        onDateTimeSelect={mockOnDateTimeSelect}
        onBack={mockOnBack}
        onCancel={mockOnCancel}
        onConfirm={mockOnConfirm}
      />
    );

    // Check that all interactive elements are accessible
    expect(screen.getByTestId("button-Back")).toBeInTheDocument();
    expect(screen.getByTestId("button-Cancel")).toBeInTheDocument();
    expect(
      screen.getByTestId("button-Confirm Appointment")
    ).toBeInTheDocument();
    expect(screen.getAllByTestId(/toggle-/)).toHaveLength(21); // 7 dates + 14 time slots
  });
});
