"use client";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";

interface PasswordInputProps {
  id: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder: string;
  disabled?: boolean;
  minLength?: number;
  required?: boolean;
  error?: boolean;
  errorMessage?: string;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  id,
  value,
  onChange,
  placeholder,
  disabled,
  minLength,
  error,
}) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="relative">
      <input
        type={showPassword ? "text" : "password"}
        id={id}
        value={value}
        onChange={onChange}
        className={`w-full px-[1.25rem] py-[0.875rem] border rounded-sm text-base ${
          error
            ? "border-[var(--error-red-4)] focus:ring-[var(--error-red-4)] focus:border-[var(--error-red-4)]"
            : "border-[var(--grey-3)] focus:outline-none focus:ring-2 focus:ring-[var(--red-6)] focus:border-transparent text-[var(--grey-7)]"
        }`}
        placeholder={placeholder}
        required
        disabled={disabled}
        minLength={minLength}
        data-ms-reveal="false"
      />
      <button
        type="button"
        onClick={() => setShowPassword(!showPassword)}
        className="cursor-pointer absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
      >
        {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
      </button>
    </div>
  );
};

export default PasswordInput;
